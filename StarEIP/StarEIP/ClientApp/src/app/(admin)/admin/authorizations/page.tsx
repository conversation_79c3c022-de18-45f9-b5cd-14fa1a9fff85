"use client";
import React, { useEffect, useState } from "react";
import AuthorizationsTable from "./AuthorizationsTable";
import { useMediaQuery } from "@mantine/hooks";
import { Authorization, ScStatusType } from "../../../../../types/Authorization";
import { Stack, Select, Button, Loader, Text, Title, rem } from "@mantine/core";
import ChildInfo from "@/app/(admin)/admin/patients/ChildInfo";
import Splitter, { Item } from "devextreme-react/splitter";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import { showNotification } from "@/app/Utils/notificationUtils";
import { ChildDetailsDtoSchema } from "@/api/types";

const AuthorizationsPage: React.FC = () => {
  const [selectedAuthorization, setSelectedAuthorization] =
    useState<Authorization | null>(null);
  const matches = useMediaQuery("(min-width: 56.25em)");
  const [scStatus, setScStatus] = useState<number | null>(null);
  const [followUpDate, setFollowUpDate] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [statusOptions, setStatusOptions] = useState<{ value: string, label: string }[]>([]);
  const [isLoadingStatusOptions, setIsLoadingStatusOptions] = useState(false);
  const [selectedChild, setSelectedChild] = useState<ChildDetailsDtoSchema | null>(null);
  const [showChildPanel, setShowChildPanel] = useState(false);

  useEffect(() => {
    fetchStatusOptions();
  }, []);

  useEffect(() => {
    if (selectedAuthorization) {
      setScStatus(selectedAuthorization.scStatus || null);
      setFollowUpDate(
        selectedAuthorization.followUpDate
          ? new Date(selectedAuthorization.followUpDate).toISOString().split("T")[0]
          : null
      );
    }
  }, [selectedAuthorization]);

  const fetchStatusOptions = async () => {
    setIsLoadingStatusOptions(true);
    try {
      const response = await axios.get<ScStatusType[]>(
        urlHelpers.getAbsoluteURL("api/authorizations/sc-status-types")
      );

      const options = response.data.map(status => ({
        value: status.id.toString(),
        label: status.name
      }));

      setStatusOptions(options);
    } catch (error) {
      console.error("Error fetching status options:", error);
      showNotification("error", "Failed to load status options");
    } finally {
      setIsLoadingStatusOptions(false);
    }
  };

  const handleUpdateStatus = async () => {
    if (!scStatus || !selectedAuthorization) return;

    setIsUpdating(true);
    try {
      await axios.post(
        urlHelpers.getAbsoluteURL(`api/authorizations/${selectedAuthorization.id}/sc-status`),
        {
          scStatus,
          followUpDate,
        },
      );

      const statusName = statusOptions.find(option => option.value === scStatus.toString())?.label || "";

      showNotification("success", `SC Status updated to ${statusName}`);

      // Update the selected authorization with new data
      setSelectedAuthorization(prev => prev ? {
        ...prev,
        scStatus,
        followUpDate: followUpDate ? new Date(followUpDate) : undefined
      } : null);
    } catch (error) {
      console.error("Error updating SC status:", error);
      showNotification("error", "Failed to update SC status");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleChildNameClick = async (childId: number) => {
    try {
      const response = await axios.get(
        urlHelpers.getAbsoluteURL(`api/children/${childId}`)
      );
      setSelectedChild(response.data);
      setShowChildPanel(true);
    } catch (error) {
      console.error("Error fetching child details:", error);
      showNotification("error", "Failed to load child details");
    }
  };

  const renderAuthorizationsTable = () => (
    <AuthorizationsTable
      onSelectAuthorizationChange={(authorization) =>
        setSelectedAuthorization(authorization)
      }
      onChildNameClick={handleChildNameClick}
    />
  );

  const renderDetails = () => (
    <Stack gap="md" style={{ height: "100%", overflow: "hidden" }}>
      {selectedAuthorization && (
        <Stack gap="md" p="md">
          <Title order={4}>Service Coordinator Status</Title>

          <Text size="sm" c="dimmed">
            Current SC Status: {selectedAuthorization.scStatusName || "Not set"}
          </Text>

          {isLoadingStatusOptions ? (
            <Loader size="sm" />
          ) : (
            <Select
              label="SC Status"
              placeholder="Select status"
              value={scStatus?.toString()}
              onChange={(value) => setScStatus(value ? parseInt(value) : null)}
              data={statusOptions}
              clearable
            />
          )}

          <Button
            onClick={handleUpdateStatus}
            loading={isUpdating}
            disabled={!scStatus}
          >
            Update Status
          </Button>
        </Stack>
      )}
    </Stack>
  );

  return (
    <>
      <Splitter id="splitter">
        <Item
          resizable={true}
          size="75%"
          minSize="70px"
          render={renderAuthorizationsTable}
        />
        {matches && (
          <Item
            resizable={true}
            minSize="250px"
            render={renderDetails}
            collapsible
          />
        )}
      </Splitter>

      {/* Child Info Side Panel */}
      {showChildPanel && selectedChild && (
        <div
          style={{
            position: "fixed",
            top: 0,
            right: 0,
            width: "400px",
            height: "100vh",
            backgroundColor: "white",
            boxShadow: "-2px 0 10px rgba(0,0,0,0.1)",
            zIndex: 1000,
            overflow: "auto",
          }}
        >
          <div style={{ padding: "16px" }}>
            <Button
              variant="subtle"
              onClick={() => setShowChildPanel(false)}
              style={{ marginBottom: "16px" }}
            >
              Close
            </Button>
            <ChildInfo
              child={selectedChild}
              showAuthorizationsTab={false}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default AuthorizationsPage;
