using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using StarEIP.Models;
using System.Security.Claims;
using StarEIP.Models.Auth;
using StarEIP.Services.Auth;
using StarEIP.Models.App;

namespace StarEIP.Controllers
{
    [Route("api/authorizations")]
    [ApiController]
    [Authorize]
    public class AuthorizationController : ControllerBase
    {
        private readonly StarEipDbContext starEipDbContext;
        private readonly ILogger<AuthorizationController> logger;

        public AuthorizationController(StarEipDbContext starEipDbContext, ILogger<AuthorizationController> logger)
        {
            this.starEipDbContext = starEipDbContext;
            this.logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int? childId = null)
        {
            try
            {
                IQueryable<AuthorizationDto> query = GetAuthorizationQuery(childId);

                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                starEipDbContext.ChangeTracker.LazyLoadingEnabled = true;
                starEipDbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                loadOptions.PrimaryKey = [nameof(AuthorizationDto.Id)];
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(query, loadOptions));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get authorizations");
                throw;
            }
        }

        [HttpGet("child/{childId}")]
        public async Task<IActionResult> GetByChildId(int childId)
        {
            var query = GetAuthorizationQuery(childId);
            return Ok(await query.ToListAsync());
        }

        private IQueryable<AuthorizationDto> GetAuthorizationQuery(int? childId)
        {
            var currentUserId = User.GetUserId();
            var canViewAllAuth = User.HasClaim(UserPermission.PermissionClaimName, UserPermission.ViewAllAuth);

            var query = starEipDbContext.Authorizations
                .Include(a => a.User)
                .Include(a => a.Child)
                .ThenInclude(c => c.ReferringPhysician)
                .Include(a => a.ScStatusType)
                .Select(a => new AuthorizationDto
                {
                    //auth properties 
                    Id = a.Id,
                    ChildId = a.ChildId,
                    ChildName = a.Child.PatientFullName,
                    AuthType = a.AuthType,
                    AuthNumber = a.AuthNumber,
                    StartDate = a.StartDate,
                    EndDate = a.EndDate,
                    Units = a.Units,
                    StatusId = a.StatusId,
                    UserId = a.UserId,
                    UserName = a.User.UserName,
                    UserFullName = a.User.FirstName + " " + a.User.LastName,
                    
                    ScStatus = a.ScStatus,
                    ScStatusName = a.ScStatusType.Name,
                    StatusLastUpdated = a.StatusLastUpdated,
                    FollowUpDate = a.FollowUpDate,
                    ScStatusLastUpdated = a.ScStatusLastUpdated
                });
            
            if (childId.HasValue)
            {
                query = query.Where(a => a.ChildId == childId.Value);
            }

            if (!canViewAllAuth)
            {
                query = query.Where(a => a.UserId == currentUserId);
            }

            return query;
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var authorization = await GetAuthorizationQuery(null).SingleOrDefaultAsync(a => a.Id == id);

                if (authorization == null)
                    return NotFound();

                return Ok(authorization);
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error in get authorization by id");
                throw;
            }
        }

        [HttpGet("Status")]
        public async Task<IActionResult> GetStatus()
        {
            var status = await starEipDbContext.ChildStatuses.ToListAsync();
            return Ok(status);
        }

        [HttpPut("Update")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> UpdateAuthorization([FromForm] int key, [FromForm] string values)
        {
            var authorization = await starEipDbContext.Authorizations.SingleAsync(r => r.Id == key);
            JsonConvert.PopulateObject(values, authorization);
            await starEipDbContext.SaveChangesAsync();
            return Ok(authorization);
        }

        [HttpPost("create")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> Create([FromBody] Authorization authorization)
        {
            // Ensure that Child and User are not being set directly
            authorization.Status = null;
            authorization.Child = null;
            authorization.User = null;

            starEipDbContext.Authorizations.Add(authorization);
            await starEipDbContext.SaveChangesAsync();
            return Ok(authorization);
        }

        [HttpPost("update/{id}")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> Update(int id, [FromBody] Authorization updatedAuthorization)
        {
            var authorization = await starEipDbContext.Authorizations.FindAsync(id);
            if (authorization == null)
            {
                return NotFound();
            }

            // Update properties
            authorization.ChildId = updatedAuthorization.ChildId;
            authorization.StatusId = updatedAuthorization.StatusId;
            authorization.AuthType = updatedAuthorization.AuthType;
            authorization.UserId = updatedAuthorization.UserId;
            authorization.AuthNumber = updatedAuthorization.AuthNumber;
            authorization.StartDate = updatedAuthorization.StartDate;
            authorization.EndDate = updatedAuthorization.EndDate;
            authorization.Units = updatedAuthorization.Units;

            await starEipDbContext.SaveChangesAsync();
            return Ok(authorization);
        }

        [HttpGet("statuses")]
        public async Task<IActionResult> GetAuthorizationStatuses()
        {
            var statuses = await starEipDbContext.AuthorizationStatuses.ToListAsync();
            return Ok(statuses);
        }
        
        [HttpGet("sc-status-types")]
        public async Task<IActionResult> GetScStatusTypes()
        {
            try
            {
                var statusTypes = await starEipDbContext.ScStatusTypes
                    .OrderBy(s => s.SortOrder)
                    .Select(s => new { s.Id, s.Name })
                    .ToListAsync();

                return Ok(statusTypes);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error getting SC status types");
                throw;
            }
        }
        
        public record AuthorizationStatusUpdateRequest(int AuthorizationId, string StatusId);

        [HttpPost("{id}/status")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> UpdateStatus(int id, [FromBody] AuthorizationStatusUpdateRequest request)
        {
            var authorization = await starEipDbContext.Authorizations.FindAsync(id);
            if (authorization == null)
            {
                return NotFound();
            }

            authorization.StatusId = request.StatusId;
            await starEipDbContext.SaveChangesAsync();

            var updatedAuthorization = await GetAuthorizationQuery(null)
                .FirstOrDefaultAsync(a => a.Id == id);
            return Ok(updatedAuthorization);
        }
        
        public record ScStatusUpdateRequest(int ScStatus, DateTime? FollowUpDate);

        [HttpPost("{id}/sc-status")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> UpdateScStatus(int id, [FromBody] ScStatusUpdateRequest request)
        {
            var authorization = await starEipDbContext.Authorizations.FindAsync(id);
            if (authorization == null)
            {
                return NotFound();
            }

            authorization.ScStatus = request.ScStatus;
            authorization.ScStatusLastUpdated = DateTime.Now;
            authorization.StatusLastUpdated = DateTime.Now;
            
            if (request.FollowUpDate.HasValue)
            {
                authorization.FollowUpDate = request.FollowUpDate;
            }

            await starEipDbContext.SaveChangesAsync();

            var updatedAuthorization = await GetAuthorizationQuery(null)
                .FirstOrDefaultAsync(a => a.Id == id);
            return Ok(updatedAuthorization);
        }
    }
}
