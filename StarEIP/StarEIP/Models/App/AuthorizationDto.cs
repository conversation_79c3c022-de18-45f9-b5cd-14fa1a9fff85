using System;

namespace StarEIP.Models.App
{
    public class AuthorizationDto 
    {
        public int Id { get; set; }
        public int ChildId { get; set; }
        public string? ChildName { get;  set; }
        public string StatusId { get; set; }
        public string? AuthType { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string UserFullName { get; set; }
        public string? AuthNumber { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? Units { get; set; }
        
        // SC-specific properties
        public int? ScStatus { get; set; }
        public string? ScStatusName { get; set; }
        public DateTime? StatusLastUpdated { get; set; }
        public DateTime? FollowUpDate { get; set; }
        public DateTime? ScStatusLastUpdated { get; set; }
    }
}
