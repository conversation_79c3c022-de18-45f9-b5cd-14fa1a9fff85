﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<IsPackable>false</IsPackable>
		<SpaRoot>ClientApp\</SpaRoot>
		<Configurations>Debug;API;Release</Configurations>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>aspnet-StarEIP-b6ce80ff-0477-425d-8c7c-78ea1e8f20ba</UserSecretsId>
		<EnableMSDeployAppOffline>true</EnableMSDeployAppOffline>
		<NoWarn>NU1603</NoWarn>
		<VersionPrefix>1.0.1</VersionPrefix>
		<VersionSuffix>local</VersionSuffix>
	</PropertyGroup>

	<ItemGroup>
		<Content Remove="$(SpaRoot)**" />
		<None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**" />
		<None Include="$(SpaRoot)**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AspNetSerilog" Version="1.8.0" />
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="Azure.Identity" Version="1.13.2" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
		<PackageReference Include="CsvHelper" Version="33.0.1" />
		<PackageReference Include="Destructurama.JsonNet" Version="4.0.2" />
		<PackageReference Include="DevExpress.AspNetCore.Reporting" Version="24.2.6" />
		<PackageReference Include="DevExpress.Blazor" Version="24.2.6" />
		<PackageReference Include="DevExpress.Blazor.PdfViewer" Version="24.2.6" />
		<PackageReference Include="DevExpress.Blazor.Reporting.JSBasedControls" Version="24.2.6" />
		<PackageReference Include="DevExpress.Blazor.Reporting.JSBasedControls.WebAssembly" Version="24.2.6" />
		<PackageReference Include="DevExpress.Blazor.Reporting.Viewer" Version="24.2.6" />
		<PackageReference Include="DevExpress.Document.Processor" Version="24.2.6" />
		<PackageReference Include="DevExpress.Drawing.Skia" Version="24.2.6" />
		<PackageReference Include="DevExpress.Pdf.Drawing" Version="24.2.6" />
		<PackageReference Include="DevExpress.Pdf.SkiaRenderer" Version="24.2.6" />
		<PackageReference Include="DevExtreme.AspNet.Data" Version="5.0.0" />
		<PackageReference Include="Fluid.Core" Version="2.22.0" />
		<PackageReference Include="Flurl" Version="4.0.0" />
		<PackageReference Include="Flurl.Http" Version="4.0.2" />
		<PackageReference Include="JsonMasking" Version="1.2.2" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" NoWarn="NU1605" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.4" NoWarn="NU1605" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="9.0.4" />
		<PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Graph" Version="5.77.0" />
		<PackageReference Include="Microsoft.Identity.Web" Version="3.8.3" />
		<PackageReference Include="Microsoft.Identity.Web.DownstreamApi" Version="3.8.3" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
		<PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
		<PackageReference Include="Serilog.Exceptions.EntityFrameworkCore" Version="8.4.0" />
		<PackageReference Include="Serilog.Exceptions.MsSqlServer" Version="8.4.0" />
		<PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
	</ItemGroup>

	<Target Name="DebugEnsureNodeEnv" BeforeTargets="Build" Condition=" '$(Configuration)' == 'Debug' And !Exists('$(SpaRoot)node_modules') And '$(build_npm)' != 'NO'">
		<!-- Ensure Node.js is installed -->
		<Exec Command="node --version" ContinueOnError="true">
			<Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
		</Exec>
		<Error Condition="'$(ErrorCode)' != '0'" Text="Node.js is required to build and run this project. To continue, please install Node.js from https://nodejs.org/, and then restart your command prompt or IDE." />
		<Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
	</Target>
	<!--<Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish" Condition=" '$(build_npm)' != 'NO'">
		--><!--As part of publishing, ensure the JS resources are freshly built in production mode--><!--
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm version $(buildNumber_npm)" Condition=" '$(Configuration)' == 'Release' And $(buildNumber) != ''" />
		<Message Importance="high" Text="(print: npm run build): ./node_modules/.bin/cross-env &quot;REACT_APP_ENV=$(REACT_APP_ENV)&quot; &quot;REACT_APP_HOST_URL=$(host_url)&quot; npm run build" />
		<Exec WorkingDirectory="$(SpaRoot)" Command="./node_modules/.bin/cross-env &quot;REACT_APP_ENV=$(REACT_APP_ENV)&quot; &quot;REACT_APP_HOST_URL=$(host_url)&quot; npm run build" />
		<Message Importance="high" Text="REACT_APP_ENV=$(REACT_APP_ENV)" />
		--><!--Include the newly-built files in the publish output--><!--
		<ItemGroup>
			<DistFiles Include="$(SpaRoot)build\**" />
			<ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
				<RelativePath>%(DistFiles.Identity)</RelativePath>
				<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
			</ResolvedFileToPublish>
		</ItemGroup>
	</Target>-->

</Project>
